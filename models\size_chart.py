# -*- coding: utf-8 -*-

from odoo import models, fields, api


class Size<PERSON>hart(models.Model):
    _name = 'website.size.chart'
    _description = 'Size Chart for Products'
    _order = 'gender, brand_id, sequence'

    name = fields.Char('Chart Name', required=True)
    gender = fields.Selection([
        ('male', 'Male'),
        ('female', 'Female'),
        ('unisex', 'Unisex')
    ], string='Gender', required=True, default='male')
    
    brand_id = fields.Many2one('product.brand', string='Brand', help='Brand-specific fitting variations')
    category_id = fields.Many2one('product.category', string='Product Category')
    
    sequence = fields.Integer('Sequence', default=10)
    active = fields.Boolean('Active', default=True)
    
    # Size chart lines
    size_line_ids = fields.One2many('website.size.chart.line', 'chart_id', string='Size Lines')
    
    # Indian sizing standards
    sizing_standard = fields.Selection([
        ('indian', 'Indian Standard'),
        ('international', 'International'),
        ('brand_specific', 'Brand Specific')
    ], string='Sizing Standard', default='indian')
    
    description = fields.Html('Description')
    
    @api.model
    def get_size_chart(self, product_id, gender=None):
        """Get appropriate size chart for a product"""
        product = self.env['product.template'].browse(product_id)
        
        domain = [('active', '=', True)]
        
        # Filter by gender if provided
        if gender:
            domain.append(('gender', 'in', [gender, 'unisex']))
        
        # Filter by brand if product has brand
        if hasattr(product, 'brand_id') and product.brand_id:
            domain.append(('brand_id', 'in', [product.brand_id.id, False]))
        
        # Filter by category
        if product.categ_id:
            domain.append(('category_id', 'in', [product.categ_id.id, False]))
        
        # Get the most specific chart (brand-specific first, then general)
        charts = self.search(domain, order='brand_id desc, sequence')
        
        return charts[0] if charts else False


class SizeChartLine(models.Model):
    _name = 'website.size.chart.line'
    _description = 'Size Chart Line'
    _order = 'sequence'

    chart_id = fields.Many2one('website.size.chart', string='Size Chart', required=True, ondelete='cascade')
    sequence = fields.Integer('Sequence', default=10)
    
    # Size information
    size_name = fields.Char('Size', required=True)  # XS, S, M, L, XL, XXL
    size_number = fields.Char('Size Number')  # 28, 30, 32, 34, 36, 38, 40, 42
    
    # Measurements in centimeters (Indian standard)
    chest_cm = fields.Float('Chest (cm)')
    waist_cm = fields.Float('Waist (cm)')
    hip_cm = fields.Float('Hip (cm)')
    shoulder_cm = fields.Float('Shoulder (cm)')
    sleeve_cm = fields.Float('Sleeve (cm)')
    length_cm = fields.Float('Length (cm)')
    
    # Additional measurements for specific garments
    neck_cm = fields.Float('Neck (cm)')
    inseam_cm = fields.Float('Inseam (cm)')
    
    # Measurements in inches (for display)
    chest_inch = fields.Float('Chest (inch)', compute='_compute_inches', store=True)
    waist_inch = fields.Float('Waist (inch)', compute='_compute_inches', store=True)
    hip_inch = fields.Float('Hip (inch)', compute='_compute_inches', store=True)
    shoulder_inch = fields.Float('Shoulder (inch)', compute='_compute_inches', store=True)
    sleeve_inch = fields.Float('Sleeve (inch)', compute='_compute_inches', store=True)
    length_inch = fields.Float('Length (inch)', compute='_compute_inches', store=True)
    neck_inch = fields.Float('Neck (inch)', compute='_compute_inches', store=True)
    inseam_inch = fields.Float('Inseam (inch)', compute='_compute_inches', store=True)
    
    @api.depends('chest_cm', 'waist_cm', 'hip_cm', 'shoulder_cm', 'sleeve_cm', 'length_cm', 'neck_cm', 'inseam_cm')
    def _compute_inches(self):
        """Convert centimeters to inches"""
        for line in self:
            line.chest_inch = line.chest_cm / 2.54 if line.chest_cm else 0
            line.waist_inch = line.waist_cm / 2.54 if line.waist_cm else 0
            line.hip_inch = line.hip_cm / 2.54 if line.hip_cm else 0
            line.shoulder_inch = line.shoulder_cm / 2.54 if line.shoulder_cm else 0
            line.sleeve_inch = line.sleeve_cm / 2.54 if line.sleeve_cm else 0
            line.length_inch = line.length_cm / 2.54 if line.length_cm else 0
            line.neck_inch = line.neck_cm / 2.54 if line.neck_cm else 0
            line.inseam_inch = line.inseam_cm / 2.54 if line.inseam_cm else 0


class ProductTemplate(models.Model):
    _inherit = 'product.template'
    
    size_chart_id = fields.Many2one('website.size.chart', string='Size Chart')
    gender = fields.Selection([
        ('male', 'Male'),
        ('female', 'Female'),
        ('unisex', 'Unisex')
    ], string='Gender')
    
    def get_size_chart(self):
        """Get size chart for this product"""
        if self.size_chart_id:
            return self.size_chart_id
        
        # Auto-detect based on gender and brand
        size_chart_obj = self.env['website.size.chart']
        return size_chart_obj.get_size_chart(self.id, self.gender)


class ProductBrand(models.Model):
    _name = 'product.brand'
    _description = 'Product Brand'
    
    name = fields.Char('Brand Name', required=True)
    description = fields.Text('Description')
    logo = fields.Binary('Logo')
    active = fields.Boolean('Active', default=True)
    
    # Brand-specific fitting notes
    fitting_notes = fields.Html('Fitting Notes', help='Brand-specific fitting information')
