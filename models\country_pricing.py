# -*- coding: utf-8 -*-

from odoo import models, fields, api


class CountryPricing(models.Model):
    _name = 'website.country.pricing'
    _description = 'Country-based Pricing Configuration'
    
    name = fields.Char('Configuration Name', required=True)
    country_id = fields.Many2one('res.country', string='Country', required=True)
    currency_id = fields.Many2one('res.currency', string='Currency', required=True)
    
    # Pricing multiplier or fixed rates
    pricing_method = fields.Selection([
        ('multiplier', 'Multiplier'),
        ('fixed_rate', 'Fixed Exchange Rate'),
        ('pricelist', 'Use Pricelist')
    ], string='Pricing Method', default='multiplier')
    
    multiplier = fields.Float('Price Multiplier', default=1.0, 
                             help='Multiply base price by this factor')
    exchange_rate = fields.Float('Exchange Rate', default=1.0,
                                help='Custom exchange rate for pricing')
    
    pricelist_id = fields.Many2one('product.pricelist', string='Pricelist')
    
    active = fields.Boolean('Active', default=True)
    sequence = fields.Integer('Sequence', default=10)
    
    # Display settings
    show_in_popup = fields.Boolean('Show in Country Popup', default=True)
    flag_image = fields.Binary('Flag Image')
    
    @api.model
    def get_country_pricing(self, country_code=None):
        """Get pricing configuration for a country"""
        if not country_code:
            return False
            
        country = self.env['res.country'].search([('code', '=', country_code)], limit=1)
        if not country:
            return False
            
        pricing = self.search([
            ('country_id', '=', country.id),
            ('active', '=', True)
        ], limit=1)
        
        return pricing
    
    @api.model
    def get_available_countries(self):
        """Get list of countries available for selection"""
        return self.search([
            ('active', '=', True),
            ('show_in_popup', '=', True)
        ], order='sequence, name')
    
    def calculate_price(self, base_price):
        """Calculate price based on configuration"""
        if self.pricing_method == 'multiplier':
            return base_price * self.multiplier
        elif self.pricing_method == 'fixed_rate':
            return base_price * self.exchange_rate
        elif self.pricing_method == 'pricelist' and self.pricelist_id:
            # This would need product context for proper pricelist calculation
            return base_price
        return base_price


class ResCountry(models.Model):
    _inherit = 'res.country'
    
    pricing_config_ids = fields.One2many('website.country.pricing', 'country_id', 
                                        string='Pricing Configurations')


class Website(models.Model):
    _inherit = 'website'
    
    def get_current_country_pricing(self):
        """Get current country pricing based on session or IP"""
        # Check session first
        country_code = self.env.context.get('country_code')
        if not country_code and hasattr(self.env, 'request'):
            country_code = self.env.request.session.get('selected_country')
        
        if country_code:
            pricing_obj = self.env['website.country.pricing']
            return pricing_obj.get_country_pricing(country_code)
        
        return False
    
    @api.model
    def set_country_preference(self, country_code):
        """Set country preference in session"""
        if hasattr(self.env, 'request'):
            self.env.request.session['selected_country'] = country_code
            
            # Also set currency if pricing config exists
            pricing_obj = self.env['website.country.pricing']
            pricing = pricing_obj.get_country_pricing(country_code)
            if pricing and pricing.currency_id:
                self.env.request.session['currency_id'] = pricing.currency_id.id
        
        return True
