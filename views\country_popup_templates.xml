<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Country Selection Popup Modal -->
    <template id="country_selection_modal" name="Country Selection Modal">
        <div class="modal fade" id="countrySelectionModal" tabindex="-1" role="dialog" aria-labelledby="countrySelectionModalLabel" aria-hidden="true" data-backdrop="static" data-keyboard="false">
            <div class="modal-dialog modal-md" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="countrySelectionModalLabel">
                            <i class="fa fa-globe"></i> Select Your Country
                        </h5>
                    </div>
                    <div class="modal-body">
                        <p class="text-center mb-4">Please select your country to see prices in your local currency</p>
                        
                        <div class="country-grid">
                            <t t-foreach="available_countries" t-as="country_config">
                                <div class="country-option" 
                                     data-country-code="country_config.country_id.code"
                                     data-currency-symbol="country_config.currency_id.symbol"
                                     data-currency-name="country_config.currency_id.name">
                                    <div class="country-card">
                                        <div class="country-flag">
                                            <t t-if="country_config.flag_image">
                                                <img t-att-src="'data:image/png;base64,' + country_config.flag_image.decode('utf-8')" 
                                                     t-att-alt="country_config.country_id.name" 
                                                     class="flag-img"/>
                                            </t>
                                            <t t-else="">
                                                <div class="flag-placeholder">
                                                    <i class="fa fa-flag"></i>
                                                </div>
                                            </t>
                                        </div>
                                        <div class="country-info">
                                            <h6 class="country-name"><t t-esc="country_config.country_id.name"/></h6>
                                            <small class="currency-info">
                                                <t t-esc="country_config.currency_id.symbol"/> 
                                                <t t-esc="country_config.currency_id.name"/>
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </t>
                        </div>
                        
                        <!-- Default/Continue without selection -->
                        <div class="text-center mt-4">
                            <button type="button" class="btn btn-link text-muted" id="continueWithoutSelection">
                                Continue without selecting country
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </template>

    <!-- Country Selection Script Include -->
    <template id="country_selection_include" name="Country Selection Include" inherit_id="website.layout" priority="20">
        <xpath expr="//body" position="inside">
            <t t-if="not request.session.get('selected_country') and not request.session.get('country_popup_shown')">
                <t t-set="available_countries" t-value="request.env['website.country.pricing'].get_available_countries()"/>
                <t t-if="available_countries">
                    <t t-call="website_size_chart_pricing.country_selection_modal"/>
                    <script>
                        document.addEventListener('DOMContentLoaded', function() {
                            // Show country selection modal on page load
                            setTimeout(function() {
                                $('#countrySelectionModal').modal('show');
                            }, 1000); // Show after 1 second
                        });
                    </script>
                </t>
            </t>
        </xpath>
    </template>

    <!-- Current Country Display -->
    <template id="current_country_display" name="Current Country Display">
        <div class="current-country-display">
            <t t-set="current_pricing" t-value="request.website.get_current_country_pricing()"/>
            <t t-if="current_pricing">
                <div class="dropdown">
                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" id="countryDropdown" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                        <i class="fa fa-globe"></i>
                        <t t-esc="current_pricing.country_id.name"/>
                        (<t t-esc="current_pricing.currency_id.symbol"/>)
                    </button>
                    <div class="dropdown-menu" aria-labelledby="countryDropdown">
                        <a class="dropdown-item" href="#" data-toggle="modal" data-target="#countrySelectionModal">
                            <i class="fa fa-edit"></i> Change Country
                        </a>
                    </div>
                </div>
            </t>
            <t t-else="">
                <button class="btn btn-sm btn-outline-secondary" data-toggle="modal" data-target="#countrySelectionModal">
                    <i class="fa fa-globe"></i> Select Country
                </button>
            </t>
        </div>
    </template>

    <!-- Add Country Display to Header -->
    <template id="header_country_display" name="Header Country Display" inherit_id="website.layout" priority="15">
        <xpath expr="//header//div[hasclass('navbar-nav')]" position="inside">
            <li class="nav-item">
                <t t-call="website_size_chart_pricing.current_country_display"/>
            </li>
        </xpath>
    </template>

    <!-- Price Display with Currency -->
    <template id="product_price_currency" name="Product Price with Currency" inherit_id="website_sale.product_price" priority="20">
        <xpath expr="//span[hasclass('oe_price')]" position="attributes">
            <attribute name="data-currency-symbol"><t t-esc="request.website.get_current_country_pricing().currency_id.symbol if request.website.get_current_country_pricing() else website.currency_id.symbol"/></attribute>
        </xpath>
    </template>
</odoo>
