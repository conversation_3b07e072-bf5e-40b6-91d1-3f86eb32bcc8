odoo.define('website_size_chart_pricing.country_pricing', function (require) {
'use strict';

var publicWidget = require('web.public.widget');
var ajax = require('web.ajax');

publicWidget.registry.CountryPricing = publicWidget.Widget.extend({
    selector: '#countrySelectionModal',
    events: {
        'click .country-option': '_onCountrySelect',
        'click #continueWithoutSelection': '_onContinueWithoutSelection',
        'show.bs.modal': '_onModalShow',
    },

    /**
     * @override
     */
    start: function () {
        this._super.apply(this, arguments);
        this.selectedCountry = null;
        return this._checkAutoShow();
    },

    /**
     * Check if modal should be shown automatically
     */
    _checkAutoShow: function () {
        // Check if country is already selected or popup was shown
        var hasSelection = this._getCookie('selected_country');
        var popupShown = this._getCookie('country_popup_shown');
        
        if (!hasSelection && !popupShown) {
            // Show modal after a delay
            setTimeout(() => {
                this.$el.modal('show');
            }, 1000);
        }
    },

    /**
     * Handle country selection
     */
    _onCountrySelect: function (ev) {
        ev.preventDefault();
        var $option = $(ev.currentTarget);
        var countryCode = $option.data('country-code');
        var currencySymbol = $option.data('currency-symbol');
        var currencyName = $option.data('currency-name');
        
        // Visual feedback
        this.$('.country-option .country-card').removeClass('selected');
        $option.find('.country-card').addClass('selected');
        
        this.selectedCountry = {
            code: countryCode,
            currency_symbol: currencySymbol,
            currency_name: currencyName
        };
        
        // Auto-close modal after selection
        setTimeout(() => {
            this._saveCountrySelection();
        }, 500);
    },

    /**
     * Handle continue without selection
     */
    _onContinueWithoutSelection: function (ev) {
        ev.preventDefault();
        this._setCookie('country_popup_shown', 'true', 30); // Remember for 30 days
        this.$el.modal('hide');
    },

    /**
     * Save country selection and update pricing
     */
    _saveCountrySelection: function () {
        if (!this.selectedCountry) {
            return;
        }
        
        var self = this;
        
        // Show loading state
        this._showLoading();
        
        // Save selection via AJAX
        ajax.jsonRpc('/shop/set_country', 'call', {
            'country_code': this.selectedCountry.code,
        }).then(function (result) {
            if (result.success) {
                // Save in cookies
                self._setCookie('selected_country', self.selectedCountry.code, 365);
                self._setCookie('country_popup_shown', 'true', 365);
                
                // Update page pricing
                self._updatePagePricing(result.pricing_data);
                
                // Close modal
                self.$el.modal('hide');
                
                // Show success message
                self._showSuccessMessage();
                
                // Reload page to apply new pricing
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            } else {
                self._showError(result.error || 'Failed to save country selection');
            }
        }).catch(function (error) {
            console.error('Country selection error:', error);
            self._showError('Failed to save country selection. Please try again.');
        }).finally(function () {
            self._hideLoading();
        });
    },

    /**
     * Update pricing display on current page
     */
    _updatePagePricing: function (pricingData) {
        if (!pricingData) return;
        
        // Update currency symbols
        $('.oe_currency_value').each(function () {
            var $price = $(this);
            var currentPrice = parseFloat($price.text().replace(/[^\d.-]/g, ''));
            
            if (!isNaN(currentPrice) && pricingData.multiplier) {
                var newPrice = currentPrice * pricingData.multiplier;
                $price.text(pricingData.currency_symbol + newPrice.toFixed(2));
            }
        });
        
        // Update currency display in header
        this._updateHeaderCurrency(pricingData);
    },

    /**
     * Update header currency display
     */
    _updateHeaderCurrency: function (pricingData) {
        var $countryDisplay = $('.current-country-display');
        if ($countryDisplay.length) {
            var displayText = pricingData.country_name + ' (' + pricingData.currency_symbol + ')';
            $countryDisplay.find('button').html('<i class="fa fa-globe"></i> ' + displayText);
        }
    },

    /**
     * Show loading state
     */
    _showLoading: function () {
        this.$('.modal-body').append('<div class="loading-overlay"><div class="loading-spinner"></div></div>');
    },

    /**
     * Hide loading state
     */
    _hideLoading: function () {
        this.$('.loading-overlay').remove();
    },

    /**
     * Show success message
     */
    _showSuccessMessage: function () {
        var message = '<div class="alert alert-success text-center">' +
                     '<i class="fa fa-check-circle"></i> Country selection saved! Updating prices...' +
                     '</div>';
        
        this.$('.modal-body').prepend(message);
        
        setTimeout(() => {
            this.$('.alert-success').fadeOut();
        }, 2000);
    },

    /**
     * Show error message
     */
    _showError: function (message) {
        var errorHtml = '<div class="alert alert-error text-center">' +
                       '<i class="fa fa-exclamation-triangle"></i> ' + message +
                       '</div>';
        
        this.$('.modal-body').prepend(errorHtml);
        
        setTimeout(() => {
            this.$('.alert-error').fadeOut();
        }, 3000);
    },

    /**
     * Handle modal show event
     */
    _onModalShow: function () {
        // Track analytics if needed
        if (typeof gtag !== 'undefined') {
            gtag('event', 'country_popup_shown', {
                'event_category': 'engagement',
                'event_label': 'country_selection'
            });
        }
    },

    /**
     * Set cookie
     */
    _setCookie: function (name, value, days) {
        var expires = "";
        if (days) {
            var date = new Date();
            date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
            expires = "; expires=" + date.toUTCString();
        }
        document.cookie = name + "=" + (value || "") + expires + "; path=/";
    },

    /**
     * Get cookie
     */
    _getCookie: function (name) {
        var nameEQ = name + "=";
        var ca = document.cookie.split(';');
        for (var i = 0; i < ca.length; i++) {
            var c = ca[i];
            while (c.charAt(0) === ' ') c = c.substring(1, c.length);
            if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);
        }
        return null;
    },
});

// Country Change Widget (for header dropdown)
publicWidget.registry.CountryChange = publicWidget.Widget.extend({
    selector: '.current-country-display',
    events: {
        'click [data-target="#countrySelectionModal"]': '_onChangeCountryClick',
    },

    /**
     * Handle change country click
     */
    _onChangeCountryClick: function (ev) {
        ev.preventDefault();
        
        // Track analytics
        if (typeof gtag !== 'undefined') {
            gtag('event', 'change_country_clicked', {
                'event_category': 'engagement',
                'event_label': 'header_country_change'
            });
        }
        
        // Modal will be shown by Bootstrap
    },
});

// Auto-detect country based on IP (optional enhancement)
publicWidget.registry.CountryAutoDetect = publicWidget.Widget.extend({
    selector: 'body',

    /**
     * @override
     */
    start: function () {
        this._super.apply(this, arguments);
        
        // Only auto-detect if no country is selected
        if (!this._getCookie('selected_country')) {
            this._detectCountry();
        }
    },

    /**
     * Detect country based on IP
     */
    _detectCountry: function () {
        // This could use a service like ipapi.co or similar
        // For now, we'll skip auto-detection and rely on manual selection
        console.log('Country auto-detection could be implemented here');
    },

    /**
     * Get cookie helper
     */
    _getCookie: function (name) {
        var nameEQ = name + "=";
        var ca = document.cookie.split(';');
        for (var i = 0; i < ca.length; i++) {
            var c = ca[i];
            while (c.charAt(0) === ' ') c = c.substring(1, c.length);
            if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);
        }
        return null;
    },
});

return {
    CountryPricing: publicWidget.registry.CountryPricing,
    CountryChange: publicWidget.registry.CountryChange,
    CountryAutoDetect: publicWidget.registry.CountryAutoDetect,
};

});
