# -*- coding: utf-8 -*-

from odoo import http
from odoo.http import request
import json


class WebsiteSizeChartPricing(http.Controller):

    @http.route('/shop/size_chart', type='json', auth='public', website=True)
    def get_size_chart(self, product_id, **kwargs):
        """Get size chart data for a product"""
        try:
            product_id = int(product_id)
            product = request.env['product.template'].browse(product_id)
            
            if not product.exists():
                return {'error': 'Product not found'}
            
            # Get size chart for the product
            size_chart = product.get_size_chart()
            
            if not size_chart:
                return {'error': 'No size chart available for this product'}
            
            # Prepare size chart data
            size_chart_data = {
                'id': size_chart.id,
                'name': size_chart.name,
                'gender': size_chart.gender,
                'sizing_standard': size_chart.sizing_standard,
                'description': size_chart.description,
                'brand_name': size_chart.brand_id.name if size_chart.brand_id else None,
                'fitting_notes': size_chart.brand_id.fitting_notes if size_chart.brand_id else None,
                'size_lines': []
            }
            
            # Add size lines
            for line in size_chart.size_line_ids:
                line_data = {
                    'size_name': line.size_name,
                    'size_number': line.size_number,
                    'measurements_cm': {
                        'chest': line.chest_cm,
                        'waist': line.waist_cm,
                        'hip': line.hip_cm,
                        'shoulder': line.shoulder_cm,
                        'sleeve': line.sleeve_cm,
                        'length': line.length_cm,
                        'neck': line.neck_cm,
                        'inseam': line.inseam_cm,
                    },
                    'measurements_inch': {
                        'chest': round(line.chest_inch, 1) if line.chest_inch else 0,
                        'waist': round(line.waist_inch, 1) if line.waist_inch else 0,
                        'hip': round(line.hip_inch, 1) if line.hip_inch else 0,
                        'shoulder': round(line.shoulder_inch, 1) if line.shoulder_inch else 0,
                        'sleeve': round(line.sleeve_inch, 1) if line.sleeve_inch else 0,
                        'length': round(line.length_inch, 1) if line.length_inch else 0,
                        'neck': round(line.neck_inch, 1) if line.neck_inch else 0,
                        'inseam': round(line.inseam_inch, 1) if line.inseam_inch else 0,
                    }
                }
                size_chart_data['size_lines'].append(line_data)
            
            return {'size_chart': size_chart_data}
            
        except Exception as e:
            return {'error': str(e)}

    @http.route('/shop/set_country', type='json', auth='public', website=True)
    def set_country(self, country_code, **kwargs):
        """Set country preference and return pricing data"""
        try:
            # Find country
            country = request.env['res.country'].search([('code', '=', country_code)], limit=1)
            if not country:
                return {'success': False, 'error': 'Country not found'}
            
            # Get pricing configuration
            pricing_obj = request.env['website.country.pricing']
            pricing = pricing_obj.get_country_pricing(country_code)
            
            if not pricing:
                return {'success': False, 'error': 'No pricing configuration for this country'}
            
            # Set country preference in session
            request.website.set_country_preference(country_code)
            
            # Prepare pricing data
            pricing_data = {
                'country_code': country_code,
                'country_name': country.name,
                'currency_symbol': pricing.currency_id.symbol,
                'currency_name': pricing.currency_id.name,
                'multiplier': pricing.multiplier if pricing.pricing_method == 'multiplier' else 1.0,
                'exchange_rate': pricing.exchange_rate if pricing.pricing_method == 'fixed_rate' else 1.0,
            }
            
            return {
                'success': True,
                'pricing_data': pricing_data
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}

    @http.route('/shop/get_countries', type='json', auth='public', website=True)
    def get_available_countries(self, **kwargs):
        """Get list of available countries for selection"""
        try:
            pricing_obj = request.env['website.country.pricing']
            countries = pricing_obj.get_available_countries()
            
            country_list = []
            for pricing in countries:
                country_data = {
                    'code': pricing.country_id.code,
                    'name': pricing.country_id.name,
                    'currency_symbol': pricing.currency_id.symbol,
                    'currency_name': pricing.currency_id.name,
                    'flag_image': pricing.flag_image.decode('utf-8') if pricing.flag_image else None,
                }
                country_list.append(country_data)
            
            return {'countries': country_list}
            
        except Exception as e:
            return {'error': str(e)}

    @http.route('/shop/product_price', type='json', auth='public', website=True)
    def get_product_price(self, product_id, **kwargs):
        """Get product price in selected currency"""
        try:
            product_id = int(product_id)
            product = request.env['product.template'].browse(product_id)
            
            if not product.exists():
                return {'error': 'Product not found'}
            
            # Get current pricing configuration
            current_pricing = request.website.get_current_country_pricing()
            
            # Get base price
            base_price = product.list_price
            
            if current_pricing:
                # Calculate price based on configuration
                final_price = current_pricing.calculate_price(base_price)
                currency_symbol = current_pricing.currency_id.symbol
            else:
                final_price = base_price
                currency_symbol = request.website.currency_id.symbol
            
            return {
                'price': final_price,
                'currency_symbol': currency_symbol,
                'formatted_price': f"{currency_symbol}{final_price:.2f}"
            }
            
        except Exception as e:
            return {'error': str(e)}


