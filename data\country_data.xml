<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        
        <!-- India (Base Country) -->
        <record id="country_pricing_india" model="website.country.pricing">
            <field name="name">India Pricing</field>
            <field name="country_id" ref="base.in"/>
            <field name="currency_id" ref="base.INR"/>
            <field name="pricing_method">multiplier</field>
            <field name="multiplier">1.0</field>
            <field name="sequence">10</field>
            <field name="show_in_popup">True</field>
        </record>

        <!-- United States -->
        <record id="country_pricing_us" model="website.country.pricing">
            <field name="name">United States Pricing</field>
            <field name="country_id" ref="base.us"/>
            <field name="currency_id" ref="base.USD"/>
            <field name="pricing_method">multiplier</field>
            <field name="multiplier">0.012</field> <!-- Approximate INR to USD conversion -->
            <field name="sequence">20</field>
            <field name="show_in_popup">True</field>
        </record>

        <!-- United Kingdom -->
        <record id="country_pricing_uk" model="website.country.pricing">
            <field name="name">United Kingdom Pricing</field>
            <field name="country_id" ref="base.gb"/>
            <field name="currency_id" ref="base.GBP"/>
            <field name="pricing_method">multiplier</field>
            <field name="multiplier">0.0095</field> <!-- Approximate INR to GBP conversion -->
            <field name="sequence">30</field>
            <field name="show_in_popup">True</field>
        </record>

        <!-- European Union (Germany as example) -->
        <record id="country_pricing_eu" model="website.country.pricing">
            <field name="name">European Union Pricing</field>
            <field name="country_id" ref="base.de"/>
            <field name="currency_id" ref="base.EUR"/>
            <field name="pricing_method">multiplier</field>
            <field name="multiplier">0.011</field> <!-- Approximate INR to EUR conversion -->
            <field name="sequence">40</field>
            <field name="show_in_popup">True</field>
        </record>

        <!-- Canada -->
        <record id="country_pricing_canada" model="website.country.pricing">
            <field name="name">Canada Pricing</field>
            <field name="country_id" ref="base.ca"/>
            <field name="currency_id" ref="base.CAD"/>
            <field name="pricing_method">multiplier</field>
            <field name="multiplier">0.016</field> <!-- Approximate INR to CAD conversion -->
            <field name="sequence">50</field>
            <field name="show_in_popup">True</field>
        </record>

        <!-- Australia -->
        <record id="country_pricing_australia" model="website.country.pricing">
            <field name="name">Australia Pricing</field>
            <field name="country_id" ref="base.au"/>
            <field name="currency_id" ref="base.AUD"/>
            <field name="pricing_method">multiplier</field>
            <field name="multiplier">0.018</field> <!-- Approximate INR to AUD conversion -->
            <field name="sequence">60</field>
            <field name="show_in_popup">True</field>
        </record>

        <!-- Japan -->
        <record id="country_pricing_japan" model="website.country.pricing">
            <field name="name">Japan Pricing</field>
            <field name="country_id" ref="base.jp"/>
            <field name="currency_id" ref="base.JPY"/>
            <field name="pricing_method">multiplier</field>
            <field name="multiplier">1.8</field> <!-- Approximate INR to JPY conversion -->
            <field name="sequence">70</field>
            <field name="show_in_popup">True</field>
        </record>

        <!-- Singapore -->
        <record id="country_pricing_singapore" model="website.country.pricing">
            <field name="name">Singapore Pricing</field>
            <field name="country_id" ref="base.sg"/>
            <field name="currency_id" ref="base.SGD"/>
            <field name="pricing_method">multiplier</field>
            <field name="multiplier">0.016</field> <!-- Approximate INR to SGD conversion -->
            <field name="sequence">80</field>
            <field name="show_in_popup">True</field>
        </record>

        <!-- UAE -->
        <record id="country_pricing_uae" model="website.country.pricing">
            <field name="name">UAE Pricing</field>
            <field name="country_id" ref="base.ae"/>
            <field name="currency_id" ref="base.AED"/>
            <field name="pricing_method">multiplier</field>
            <field name="multiplier">0.044</field> <!-- Approximate INR to AED conversion -->
            <field name="sequence">90</field>
            <field name="show_in_popup">True</field>
        </record>

        <!-- South Africa -->
        <record id="country_pricing_south_africa" model="website.country.pricing">
            <field name="name">South Africa Pricing</field>
            <field name="country_id" ref="base.za"/>
            <field name="currency_id" ref="base.ZAR"/>
            <field name="pricing_method">multiplier</field>
            <field name="multiplier">0.22</field> <!-- Approximate INR to ZAR conversion -->
            <field name="sequence">100</field>
            <field name="show_in_popup">True</field>
        </record>

    </data>
</odoo>

<!-- Note: Exchange rates are approximate and should be updated regularly.
     In a production environment, you might want to integrate with a currency
     exchange API to get real-time rates. -->
