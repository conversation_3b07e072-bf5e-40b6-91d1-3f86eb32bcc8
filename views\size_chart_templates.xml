<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Size Chart Modal Template -->
    <template id="size_chart_modal" name="Size Chart Modal">
        <div class="modal fade" id="sizeChartModal" tabindex="-1" role="dialog" aria-labelledby="sizeChartModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="sizeChartModalLabel">
                            <i class="fa fa-ruler"></i> Size Chart
                            <span t-if="size_chart.brand_id" class="text-muted">- <t t-esc="size_chart.brand_id.name"/></span>
                        </h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <i class="fa fa-times" aria-hidden="true"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <!-- Unit Toggle -->
                        <div class="text-center mb-3">
                            <div class="btn-group unit-toggle" role="group" aria-label="Unit Toggle">
                                <button type="button" class="btn btn-outline-primary active" data-unit="cm">
                                    <img src="/website_size_chart_pricing/static/src/img/cm-icon.png" alt="CM" class="unit-icon"/> 
                                    Centimeters
                                </button>
                                <button type="button" class="btn btn-outline-primary" data-unit="inch">
                                    <img src="/website_size_chart_pricing/static/src/img/inch-icon.png" alt="Inch" class="unit-icon"/> 
                                    Inches
                                </button>
                            </div>
                        </div>
                        
                        <!-- Size Chart Table -->
                        <div class="size-chart-container">
                            <div class="table-responsive">
                                <table class="table table-bordered size-chart-table">
                                    <thead class="thead-light">
                                        <tr>
                                            <th>Size</th>
                                            <th t-if="any(line.size_number for line in size_chart.size_line_ids)">Size Number</th>
                                            <th class="measurement-col" data-measurement="chest">Chest</th>
                                            <th class="measurement-col" data-measurement="waist">Waist</th>
                                            <th class="measurement-col" data-measurement="hip">Hip</th>
                                            <th class="measurement-col" data-measurement="shoulder">Shoulder</th>
                                            <th class="measurement-col" data-measurement="sleeve">Sleeve</th>
                                            <th class="measurement-col" data-measurement="length">Length</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr t-foreach="size_chart.size_line_ids" t-as="line">
                                            <td class="font-weight-bold"><t t-esc="line.size_name"/></td>
                                            <td t-if="any(l.size_number for l in size_chart.size_line_ids)">
                                                <t t-esc="line.size_number"/>
                                            </td>
                                            <td class="measurement-value" 
                                                t-att-data-cm="line.chest_cm" 
                                                t-att-data-inch="'%.1f' % line.chest_inch if line.chest_inch else ''">
                                                <span class="cm-value"><t t-esc="'%.0f' % line.chest_cm if line.chest_cm else '-'"/></span>
                                                <span class="inch-value d-none"><t t-esc="'%.1f' % line.chest_inch if line.chest_inch else '-'"/></span>
                                            </td>
                                            <td class="measurement-value" 
                                                t-att-data-cm="line.waist_cm" 
                                                t-att-data-inch="'%.1f' % line.waist_inch if line.waist_inch else ''">
                                                <span class="cm-value"><t t-esc="'%.0f' % line.waist_cm if line.waist_cm else '-'"/></span>
                                                <span class="inch-value d-none"><t t-esc="'%.1f' % line.waist_inch if line.waist_inch else '-'"/></span>
                                            </td>
                                            <td class="measurement-value" 
                                                t-att-data-cm="line.hip_cm" 
                                                t-att-data-inch="'%.1f' % line.hip_inch if line.hip_inch else ''">
                                                <span class="cm-value"><t t-esc="'%.0f' % line.hip_cm if line.hip_cm else '-'"/></span>
                                                <span class="inch-value d-none"><t t-esc="'%.1f' % line.hip_inch if line.hip_inch else '-'"/></span>
                                            </td>
                                            <td class="measurement-value" 
                                                t-att-data-cm="line.shoulder_cm" 
                                                t-att-data-inch="'%.1f' % line.shoulder_inch if line.shoulder_inch else ''">
                                                <span class="cm-value"><t t-esc="'%.0f' % line.shoulder_cm if line.shoulder_cm else '-'"/></span>
                                                <span class="inch-value d-none"><t t-esc="'%.1f' % line.shoulder_inch if line.shoulder_inch else '-'"/></span>
                                            </td>
                                            <td class="measurement-value" 
                                                t-att-data-cm="line.sleeve_cm" 
                                                t-att-data-inch="'%.1f' % line.sleeve_inch if line.sleeve_inch else ''">
                                                <span class="cm-value"><t t-esc="'%.0f' % line.sleeve_cm if line.sleeve_cm else '-'"/></span>
                                                <span class="inch-value d-none"><t t-esc="'%.1f' % line.sleeve_inch if line.sleeve_inch else '-'"/></span>
                                            </td>
                                            <td class="measurement-value" 
                                                t-att-data-cm="line.length_cm" 
                                                t-att-data-inch="'%.1f' % line.length_inch if line.length_inch else ''">
                                                <span class="cm-value"><t t-esc="'%.0f' % line.length_cm if line.length_cm else '-'"/></span>
                                                <span class="inch-value d-none"><t t-esc="'%.1f' % line.length_inch if line.length_inch else '-'"/></span>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        
                        <!-- Brand Fitting Notes -->
                        <div t-if="size_chart.brand_id and size_chart.brand_id.fitting_notes" class="mt-3">
                            <h6><i class="fa fa-info-circle"></i> Brand Fitting Notes</h6>
                            <div class="alert alert-info">
                                <t t-raw="size_chart.brand_id.fitting_notes"/>
                            </div>
                        </div>
                        
                        <!-- General Description -->
                        <div t-if="size_chart.description" class="mt-3">
                            <h6><i class="fa fa-info-circle"></i> Size Guide</h6>
                            <div class="size-guide-description">
                                <t t-raw="size_chart.description"/>
                            </div>
                        </div>
                        
                        <!-- Sizing Standard Info -->
                        <div class="mt-3">
                            <small class="text-muted">
                                <i class="fa fa-tag"></i> 
                                Sizing Standard: 
                                <span t-if="size_chart.sizing_standard == 'indian'">Indian Standard</span>
                                <span t-elif="size_chart.sizing_standard == 'international'">International</span>
                                <span t-else="">Brand Specific</span>
                            </small>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
        </div>
    </template>

    <!-- Size Chart Button for Product Pages -->
    <template id="product_size_chart_button" name="Size Chart Button" inherit_id="website_sale.product" priority="20">
        <xpath expr="//div[@id='product_details']//div[hasclass('js_product')]" position="inside">
            <div t-if="product.get_size_chart()" class="mt-3">
                <button type="button" class="btn btn-outline-info btn-sm" 
                        data-toggle="modal" 
                        data-target="#sizeChartModal"
                        data-product-id="product.id">
                    <i class="fa fa-ruler"></i> Size Chart
                </button>
            </div>
        </xpath>
    </template>

    <!-- Include Size Chart Modal in Product Pages -->
    <template id="product_size_chart_modal_include" name="Include Size Chart Modal" inherit_id="website_sale.product" priority="30">
        <xpath expr="//div[@id='wrap']" position="after">
            <t t-set="size_chart" t-value="product.get_size_chart()"/>
            <t t-if="size_chart">
                <t t-call="website_size_chart_pricing.size_chart_modal"/>
            </t>
        </xpath>
    </template>
</odoo>
