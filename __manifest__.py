{
    'name': 'Website Size Chart & Country Pricing',
    'version': '********.0',
    'category': 'Website/Website',
    'summary': 'Dynamic size charts with gender/brand support and country-based pricing',
    'description': """
        Website Size Chart & Country Pricing Module
        ==========================================

        Features:
        - Dynamic size charts based on gender (Male/Female) and brand
        - Indian sizing standards support
        - Brand-specific fitting variations
        - Image-based unit switching (inch/cm)
        - Country-based pricing popup
        - Responsive popup modals

        Note: This module provides the framework without demo data.
        Configure your own size charts and country pricing as needed.
    """,
    'author': 'ArihantAi',
    'website': 'https://www.arihantai.com',
    'depends': [
        'base',
        'website',
        'website_sale',
        'product',
        'sale',
    ],
    'data': [
        'security/ir.model.access.csv',
        'views/size_chart_templates.xml',
        'views/country_popup_templates.xml',
        'views/product_template_views.xml',
        'views/website_templates.xml',
    ],
    'assets': {
        'web.assets_frontend': [
            'website_size_chart_pricing/static/src/css/modals.css',
            'website_size_chart_pricing/static/src/js/size_chart.js',
            'website_size_chart_pricing/static/src/js/country_pricing.js',
        ],
    },
    'installable': True,
    'auto_install': False,
    'application': False,
}

